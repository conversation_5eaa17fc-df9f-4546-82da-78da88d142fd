'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { RequisitionForm } from '@/components/procurement/forms/requisition-form'
import { toast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useProcurementStore } from '@/lib/stores/procurement-store'

interface CreateRequisitionPageProps {
  userId: string
}

interface Department {
  _id: string
  name: string
}

interface Budget {
  _id: string
  name: string
  fiscalYear: string
}

interface User {
  _id: string
  firstName: string
  lastName: string
  email: string
  department?: string
}

export function CreateRequisitionPage({ userId }: CreateRequisitionPageProps) {
  const router = useRouter()
  const [isDataLoading, setIsDataLoading] = useState(true)
  const [departments, setDepartments] = useState<Department[]>([])
  const [budgets, setBudgets] = useState<Budget[]>([])
  const [currentUser, setCurrentUser] = useState<User | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Use Zustand store
  const { createRequisition, isLoadingRequisitions } = useProcurementStore()

  // Procurement categories
  const categories = [
    { value: 'office-supplies', label: 'Office Supplies' },
    { value: 'equipment', label: 'Equipment' },
    { value: 'software', label: 'Software' },
    { value: 'services', label: 'Services' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'training', label: 'Training' },
    { value: 'travel', label: 'Travel' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'other', label: 'Other' }
  ]

  // Fetch required data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsDataLoading(true)
        setError(null)

        // Fetch departments, budgets, and current user in parallel
        const [departmentsRes, budgetsRes, userRes] = await Promise.all([
          fetch('/api/hr/departments?limit=100'),
          fetch('/api/accounting/budget?limit=100'),
          fetch(`/api/users/${userId}`)
        ])

        // Check if all requests were successful
        if (!departmentsRes.ok) {
          throw new Error('Failed to fetch departments')
        }
        if (!budgetsRes.ok) {
          console.warn('Failed to fetch budgets - continuing without budget data', {
            status: budgetsRes.status,
            statusText: budgetsRes.statusText
          })
        }
        if (!userRes.ok) {
          throw new Error('Failed to fetch user data')
        }

        // Parse responses
        const departmentsData = await departmentsRes.json()
        const budgetsData = budgetsRes.ok ? await budgetsRes.json() : { budgets: [] }
        const userData = await userRes.json()

        // Set state
        setDepartments(departmentsData.data?.docs || [])
        setBudgets(budgetsData.budgets || [])
        setCurrentUser({
          _id: userData._id,
          firstName: userData.firstName,
          lastName: userData.lastName,
          email: userData.email,
          department: userData.department?._id || userData.departmentId
        })

      } catch (error) {
        console.error('Error fetching data:', error)
        setError(error instanceof Error ? error.message : 'Failed to load required data')
        toast({
          title: "Error",
          description: "Failed to load required data. Please refresh the page.",
          variant: "destructive",
        })
      } finally {
        setIsDataLoading(false)
      }
    }

    if (userId) {
      fetchData()
    }
  }, [userId])

  const handleSubmit = async (data: any) => {
    try {
      console.log('handleSubmit called with data:', data)

      // Use Zustand store to create requisition
      const result = await createRequisition({
        ...data,
        createdBy: userId,
        requestedBy: data.requestedBy || userId,
      })

      if (result) {
        // Redirect to requisitions list on success
        router.push('/dashboard/procurement/requisitions')
      }

    } catch (error) {
      console.error('Error creating requisition:', error)
      // Error handling is done in the store
    }
  }

  // Show loading state while fetching data
  if (isDataLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Create Purchase Requisition</CardTitle>
          <CardDescription>Loading required data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading departments and budget data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show error state if data loading failed
  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Create Purchase Requisition</CardTitle>
          <CardDescription>Error loading required data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <RequisitionForm
      onSubmit={handleSubmit}
      isLoading={isLoadingRequisitions}
      departments={departments}
      budgets={budgets}
      categories={categories}
      currentUser={currentUser ? {
        _id: currentUser._id,
        name: `${currentUser.firstName} ${currentUser.lastName}`,
        department: currentUser.department
      } : undefined}
    />
  )
}
