// Script to fix requisition numbers for existing documents
// Run this script to update existing requisitions with null requisitionNumber

const mongoose = require('mongoose');

// MongoDB connection string - update this to match your database
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://winstonmhango23:<EMAIL>/?retryWrites=true&w=majority&appName=resources';

async function fixRequisitionNumbers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get the requisitions collection
    const db = mongoose.connection.db;
    const collection = db.collection('requisitions');

    // Find all documents with null requisitionNumber
    const documentsWithNullNumber = await collection.find({
      requisitionNumber: null
    }).toArray();

    console.log(`Found ${documentsWithNullNumber.length} documents with null requisitionNumber`);

    if (documentsWithNullNumber.length === 0) {
      console.log('No documents need fixing');
      return;
    }

    // Update each document with a unique requisitionNumber
    for (let i = 0; i < documentsWithNullNumber.length; i++) {
      const doc = documentsWithNullNumber[i];
      const date = new Date(doc.createdAt || doc.date || new Date());
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      
      // Generate a unique number based on the document's creation date and index
      const requisitionNumber = `REQ-${year}${month}-${String(i + 1).padStart(4, '0')}`;
      
      // Update the document
      await collection.updateOne(
        { _id: doc._id },
        { $set: { requisitionNumber } }
      );
      
      console.log(`Updated document ${doc._id} with requisitionNumber: ${requisitionNumber}`);
    }

    console.log('All documents updated successfully');

  } catch (error) {
    console.error('Error fixing requisition numbers:', error);
  } finally {
    // Close the connection
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
fixRequisitionNumbers();
