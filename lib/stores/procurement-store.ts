// lib/stores/procurement-store.ts
'use client';

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { toast } from '@/components/ui/use-toast';

// Contract interfaces
export interface Contract {
  _id: string;
  contractNumber: string;
  title: string;
  description?: string;
  supplier: {
    _id: string;
    name: string;
    email: string;
  };
  type: 'service' | 'goods' | 'works' | 'consultancy';
  status: 'draft' | 'active' | 'suspended' | 'terminated' | 'expired' | 'completed';
  value: number;
  currency: string;
  startDate: Date;
  endDate: Date;
  terms: {
    paymentTerms: string;
    deliveryTerms: string;
    warrantyPeriod?: number;
    penaltyClause?: string;
  };
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    specifications?: string;
  }>;
  milestones?: Array<{
    title: string;
    description: string;
    dueDate: Date;
    status: 'pending' | 'in_progress' | 'completed' | 'overdue';
    completionDate?: Date;
  }>;
  compliance: {
    score: number;
    lastReviewDate: Date;
    issues: Array<{
      type: string;
      description: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      status: 'open' | 'resolved';
      reportedDate: Date;
    }>;
  };
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Delivery interfaces
export interface Delivery {
  _id: string;
  deliveryNumber: string;
  purchaseOrderId: string;
  purchaseOrder?: string;
  contractId?: string;
  contract?: string;
  supplierId: string;
  supplier: {
    _id: string;
    name: string;
    contactPerson: string;
    email: string;
    phone: string;
  };

  // Delivery scheduling
  expectedDate: Date;
  promisedDate?: Date;
  actualDate?: Date;

  // Delivery status and tracking
  status: 'scheduled' | 'in_transit' | 'delivered' | 'partially_delivered' | 'delayed' | 'cancelled' | 'returned';
  deliveryType: 'full' | 'partial' | 'split' | 'emergency';
  priority: 'low' | 'normal' | 'high' | 'urgent';

  // Shipping and logistics
  trackingNumber?: string;
  carrier?: string;
  shippingMethod: 'standard' | 'express' | 'overnight' | 'pickup' | 'direct';
  shippingCost?: number;

  // Delivery location and contact
  deliveryAddress: {
    street: string;
    city: string;
    state?: string;
    postalCode?: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  contactPerson: string;
  contactPhone: string;
  contactEmail?: string;

  // Items and quantities
  items: Array<{
    _id?: string;
    purchaseOrderItemId: string;
    itemDescription: string;
    quantityOrdered: number;
    quantityDelivered: number;
    unitPrice: number;
    totalValue: number;
    condition: 'good' | 'damaged' | 'incomplete';
    notes?: string;
  }>;
  totalItems: number;
  totalValue: number;
  currency: string;

  // Goods receipt and inspection
  receivedBy?: string;
  receivedDate?: Date;
  goodsReceiptNumber?: string;

  // Receipt information
  receipt?: {
    received: boolean;
    receivedBy: string;
    receivedDate: Date;
    condition: 'good' | 'damaged' | 'incomplete';
    notes?: string;
    documents: string[];
  };

  // Quality inspection
  inspection?: {
    inspectedBy: string;
    inspectionDate: Date;
    passed: boolean;
    score: number;
    findings: Array<{
      item: string;
      issue: string;
      severity: 'minor' | 'major' | 'critical';
      action: 'accept' | 'reject' | 'conditional';
    }>;
    notes?: string;
  };

  // Documentation and compliance
  packingList: boolean;
  invoice: boolean;
  deliveryNote: boolean;
  qualityCertificates: boolean;
  customsDocuments: boolean;

  // Additional fields
  location?: string; // For backward compatibility
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Category interfaces
export interface ProcurementCategory {
  _id: string;
  name: string;
  code: string;
  description?: string;
  parentCategory?: {
    _id: string;
    name: string;
    code: string;
  };
  level: number;
  path: string;
  budgetCategory?: {
    _id: string;
    name: string;
    type: string;
  };
  approvalLimit?: number;
  requiredApprovers: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  autoApprovalThreshold?: number;
  defaultSuppliers?: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  restrictedSuppliers?: Array<{
    _id: string;
    name: string;
    email: string;
  }>;
  requiresQuotation: boolean;
  minimumQuotations?: number;
  leadTime?: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  isRestricted: boolean;
  complianceRequirements?: string[];
  requiredDocuments?: string[];
  requiresInspection: boolean;
  budgetCheckRequired: boolean;
  allowOverBudget: boolean;
  tags?: string[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Filters interfaces
export interface ContractFilters {
  supplier?: string;
  type?: string;
  status?: string;
  minValue?: number;
  maxValue?: number;
  startDate?: string;
  endDate?: string;
  search?: string;
}

export interface DeliveryFilters {
  supplier?: string;
  status?: string;
  scheduledStartDate?: string;
  scheduledEndDate?: string;
  location?: string;
  search?: string;
}

export interface CategoryFilters {
  parentCategory?: string;
  riskLevel?: string;
  isActive?: boolean;
  isRestricted?: boolean;
  requiresQuotation?: boolean;
  search?: string;
}

// Pagination interface
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Supplier interfaces
export interface Supplier {
  _id: string;
  name: string;
  supplierId: string;
  category: string[];
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  contactPerson: string;
  email: string;
  phone: string;
  website?: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  taxId?: string;
  businessLicense?: string;
  registrationNumber?: string;
  paymentTerms: string;
  currency: string;
  creditLimit: number;
  bankDetails: {
    bankName: string;
    accountNumber: string;
    branchCode: string;
    swiftCode?: string;
  };
  rating: number;
  leadTime: string;
  minimumOrderValue: number;
  certifications: string[];
  insurance: {
    provider: string;
    policyNumber: string;
    coverage: number;
    validUntil?: Date;
  };
  specializations: string[];
  servicesOffered: string[];
  qualityStandards: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Purchase Order interfaces
export interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  title: string;
  description?: string;
  type: 'goods' | 'services' | 'works';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  requisitionId?: string;
  contractId?: string;
  supplierId: string;
  supplier: {
    _id: string;
    name: string;
    email: string;
  };
  status: 'draft' | 'pending' | 'approved' | 'rejected' | 'cancelled' | 'completed';
  orderDate: Date;
  requiredDate: Date;
  expectedDeliveryDate: Date;
  actualDeliveryDate?: Date;
  items: Array<{
    requisitionItemId?: string;
    name: string;
    description: string;
    quantity: number;
    unit: string;
    unitPrice: number;
    totalPrice: number;
    tax: number;
    discount: number;
    budgetCode?: string;
    accountCode?: string;
    notes?: string;
  }>;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  paymentTerms: string;
  deliveryTerms: string;
  notes?: string;
  attachments: string[];
  approvals: Array<{
    approver: string;
    status: 'pending' | 'approved' | 'rejected';
    date?: Date;
    comments?: string;
  }>;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Requisition interfaces
export interface Requisition {
  _id: string;
  requisitionId: string;
  title: string;
  description?: string;
  requestedBy: string;
  departmentId: string;
  department: {
    _id: string;
    name: string;
  };
  date: Date;
  requiredDate: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'draft' | 'submitted' | 'approved' | 'rejected' | 'cancelled' | 'completed';
  items: Array<{
    name: string;
    description: string;
    quantity: number;
    unit: string;
    estimatedUnitPrice: number;
    totalPrice: number;
    category: string;
    urgency: 'low' | 'medium' | 'high';
    notes?: string;
  }>;
  totalAmount: number;
  budgetId?: string;
  currency: string;
  purpose: string;
  justification: string;
  businessCase?: string;
  notes?: string;
  attachments: string[];
  tags: string[];
  approvals: Array<{
    approver: string;
    status: 'pending' | 'approved' | 'rejected';
    date?: Date;
    comments?: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

// Tender interfaces
export interface Tender {
  _id: string;
  tenderNumber: string;
  title: string;
  description: string;
  category: string;
  department: string;
  publishDate: Date;
  closingDate: Date;
  estimatedValue?: number;
  currency: string;
  status: 'draft' | 'published' | 'closed' | 'awarded' | 'cancelled';
  requirements: Array<{
    description: string;
    isRequired: boolean;
    weight: number;
  }>;
  evaluationCriteria: Array<{
    name: string;
    weight: number;
    description: string;
  }>;
  notes?: string;
  attachments: string[];
  inviteAllSuppliers: boolean;
  invitedSuppliers: string[];
  allowLateSubmissions: boolean;
  requireTechnicalProposal: boolean;
  requireFinancialProposal: boolean;
  minimumBidAmount?: number;
  maximumBidAmount?: number;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Inventory interfaces
export interface ProcurementInventory {
  _id: string;
  inventoryId: string;
  name: string;
  description?: string;
  category: ProcurementCategory;
  sku?: string;
  barcode?: string;
  currentStock: number;
  minimumStock: number;
  maximumStock?: number;
  unit: string;
  unitPrice: number;
  totalValue: number;
  currency: string;
  lastPurchasePrice?: number;
  averageCost?: number;
  location: string;
  warehouse?: string;
  shelf?: string;
  zone?: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'on_order' | 'discontinued' | 'obsolete';
  reorderPoint: number;
  reorderQuantity: number;
  leadTime?: number;
  preferredSuppliers: Supplier[];
  lastPurchaseOrder?: PurchaseOrder;
  lastPurchaseDate?: string;
  nextReorderDate?: string;
  qualityGrade?: 'A' | 'B' | 'C';
  expiryDate?: string;
  batchNumber?: string;
  serialNumbers?: string[];
  tags: string[];
  notes?: string;
  attachments: string[];
  createdAt: string;
  updatedAt: string;
}

export interface InventoryFilters {
  category?: string;
  location?: string;
  status?: string;
  lowStock?: boolean;
  search?: string;
  tags?: string[];
}

export interface ReorderSuggestion {
  item: ProcurementInventory;
  suggestedQuantity: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  estimatedCost: number;
  preferredSupplier?: string;
}

export interface StockLevelReport {
  totalItems: number;
  inStock: number;
  lowStock: number;
  outOfStock: number;
  onOrder: number;
  totalValue: number;
}

// Store state interface
interface ProcurementState {
  // Contracts
  contracts: Contract[];
  selectedContract: Contract | null;
  isLoadingContracts: boolean;
  contractsError: string | null;
  contractFilters: ContractFilters;
  contractsPagination: Pagination;

  // Deliveries
  deliveries: Delivery[];
  selectedDelivery: Delivery | null;
  isLoadingDeliveries: boolean;
  deliveriesError: string | null;
  deliveryFilters: DeliveryFilters;
  deliveriesPagination: Pagination;

  // Categories
  categories: ProcurementCategory[];
  categoryHierarchy: any[];
  selectedCategory: ProcurementCategory | null;
  isLoadingCategories: boolean;
  categoriesError: string | null;
  categoryFilters: CategoryFilters;
  categoriesPagination: Pagination;

  // Suppliers
  suppliers: Supplier[];
  selectedSupplier: Supplier | null;
  isLoadingSuppliers: boolean;
  suppliersError: string | null;
  supplierFilters: any;
  suppliersPagination: Pagination;

  // Budget Categories
  budgetCategories: Array<{ _id: string; name: string; description?: string }>;
  isLoadingBudgetCategories: boolean;
  budgetCategoriesError: string | null;

  // Cost Centers
  costCenters: Array<{ _id: string; name: string; code: string; description?: string }>;
  isLoadingCostCenters: boolean;
  costCentersError: string | null;

  // Purchase Orders
  purchaseOrders: PurchaseOrder[];
  selectedPurchaseOrder: PurchaseOrder | null;
  isLoadingPurchaseOrders: boolean;
  purchaseOrdersError: string | null;
  purchaseOrderFilters: any;
  purchaseOrdersPagination: Pagination;

  // Requisitions
  requisitions: Requisition[];
  selectedRequisition: Requisition | null;
  isLoadingRequisitions: boolean;
  requisitionsError: string | null;
  requisitionFilters: any;
  requisitionsPagination: Pagination;

  // Tenders
  tenders: Tender[];
  selectedTender: Tender | null;
  isLoadingTenders: boolean;
  tendersError: string | null;
  tenderFilters: any;
  tendersPagination: Pagination;

  // Inventory
  inventoryItems: ProcurementInventory[];
  inventory: ProcurementInventory[];
  selectedInventoryItem: ProcurementInventory | null;
  isLoadingInventory: boolean;
  inventoryError: string | null;
  inventoryFilters: InventoryFilters;
  inventoryPagination: Pagination;
  reorderSuggestions: ReorderSuggestion[];
  stockLevelReport: StockLevelReport | null;
  isLoadingReorderSuggestions: boolean;
  isLoadingStockReport: boolean;

  // Cache
  contractsCache: Record<string, { data: Contract[]; timestamp: number }>;
  deliveriesCache: Record<string, { data: Delivery[]; timestamp: number }>;
  categoriesCache: Record<string, { data: ProcurementCategory[]; timestamp: number }>;
  suppliersCache: Record<string, { data: Supplier[]; timestamp: number }>;
  purchaseOrdersCache: Record<string, { data: PurchaseOrder[]; timestamp: number }>;
  requisitionsCache: Record<string, { data: Requisition[]; timestamp: number }>;
  tendersCache: Record<string, { data: Tender[]; timestamp: number }>;
  inventoryCache: Record<string, { data: ProcurementInventory[]; timestamp: number }>;
  hierarchyCache: { data: any[]; timestamp: number } | null;
  cacheDuration: number;

  // Actions
  // Contract actions
  fetchContracts: (page?: number, limit?: number, filters?: ContractFilters) => Promise<void>;
  fetchContract: (id: string) => Promise<void>;
  createContract: (data: any) => Promise<Contract | null>;
  updateContract: (id: string, data: any) => Promise<Contract | null>;
  deleteContract: (id: string) => Promise<boolean>;
  renewContract: (id: string, data: any) => Promise<Contract | null>;
  terminateContract: (id: string, data: any) => Promise<Contract | null>;
  setSelectedContract: (contract: Contract | null) => void;
  setContractFilters: (filters: ContractFilters) => void;
  clearContractsError: () => void;

  // Delivery actions
  fetchDeliveries: (page?: number, limit?: number, filters?: DeliveryFilters) => Promise<void>;
  fetchDelivery: (id: string) => Promise<void>;
  createDelivery: (data: any) => Promise<Delivery | null>;
  updateDelivery: (id: string, data: any) => Promise<Delivery | null>;
  deleteDelivery: (id: string) => Promise<boolean>;
  updateDeliveryStatus: (id: string, status: string, data?: any) => Promise<Delivery | null>;
  recordGoodsReceipt: (id: string, data: any) => Promise<Delivery | null>;
  conductInspection: (id: string, data: any) => Promise<Delivery | null>;
  setSelectedDelivery: (delivery: Delivery | null) => void;
  setDeliveryFilters: (filters: DeliveryFilters) => void;
  clearDeliveriesError: () => void;

  // Category actions
  fetchCategories: (page?: number, limit?: number, filters?: CategoryFilters) => Promise<void>;
  fetchCategoryHierarchy: (rootId?: string) => Promise<void>;
  fetchCategory: (id: string) => Promise<void>;
  createCategory: (data: any) => Promise<ProcurementCategory | null>;
  updateCategory: (id: string, data: any) => Promise<ProcurementCategory | null>;
  deleteCategory: (id: string) => Promise<boolean>;
  validateApprovalLimits: (categoryId: string, amount: number, userId?: string) => Promise<any>;
  setSelectedCategory: (category: ProcurementCategory | null) => void;
  setCategoryFilters: (filters: CategoryFilters) => void;
  clearCategoriesError: () => void;

  // Supplier actions
  fetchSuppliers: (page?: number, limit?: number, filters?: any) => Promise<void>;
  fetchSupplier: (id: string) => Promise<void>;
  createSupplier: (data: any) => Promise<Supplier | null>;
  updateSupplier: (id: string, data: any) => Promise<Supplier | null>;
  deleteSupplier: (id: string) => Promise<boolean>;
  setSelectedSupplier: (supplier: Supplier | null) => void;
  setSupplierFilters: (filters: any) => void;
  clearSuppliersError: () => void;

  // Budget Category actions
  fetchBudgetCategories: () => Promise<void>;
  clearBudgetCategoriesError: () => void;

  // Cost Center actions
  fetchCostCenters: () => Promise<void>;
  clearCostCentersError: () => void;

  // Purchase Order actions
  fetchPurchaseOrders: (page?: number, limit?: number, filters?: any) => Promise<void>;
  fetchPurchaseOrder: (id: string) => Promise<void>;
  createPurchaseOrder: (data: any) => Promise<PurchaseOrder | null>;
  updatePurchaseOrder: (id: string, data: any) => Promise<PurchaseOrder | null>;
  deletePurchaseOrder: (id: string) => Promise<boolean>;
  approvePurchaseOrder: (id: string, data: any) => Promise<PurchaseOrder | null>;
  setSelectedPurchaseOrder: (purchaseOrder: PurchaseOrder | null) => void;
  setPurchaseOrderFilters: (filters: any) => void;
  clearPurchaseOrdersError: () => void;

  // Requisition actions
  fetchRequisitions: (page?: number, limit?: number, filters?: any) => Promise<void>;
  fetchRequisition: (id: string) => Promise<void>;
  createRequisition: (data: any) => Promise<Requisition | null>;
  updateRequisition: (id: string, data: any) => Promise<Requisition | null>;
  deleteRequisition: (id: string) => Promise<boolean>;
  approveRequisition: (id: string, data: any) => Promise<Requisition | null>;
  setSelectedRequisition: (requisition: Requisition | null) => void;
  setRequisitionFilters: (filters: any) => void;
  clearRequisitionsError: () => void;

  // Tender actions
  fetchTenders: (page?: number, limit?: number, filters?: any) => Promise<void>;
  fetchTender: (id: string) => Promise<void>;
  createTender: (data: any) => Promise<Tender | null>;
  updateTender: (id: string, data: any) => Promise<Tender | null>;
  deleteTender: (id: string) => Promise<boolean>;
  publishTender: (id: string) => Promise<Tender | null>;
  closeTender: (id: string) => Promise<Tender | null>;
  setSelectedTender: (tender: Tender | null) => void;
  setTenderFilters: (filters: any) => void;
  clearTendersError: () => void;

  // Inventory actions
  fetchInventory: (page?: number, limit?: number, filters?: InventoryFilters) => Promise<void>;
  fetchInventoryItem: (id: string) => Promise<void>;
  createInventoryItem: (data: any) => Promise<ProcurementInventory | null>;
  updateInventoryItem: (id: string, data: any) => Promise<ProcurementInventory | null>;
  deleteInventoryItem: (id: string) => Promise<boolean>;
  updateStock: (itemId: string, quantity: number, operation: 'add' | 'remove' | 'set', reason?: string) => Promise<ProcurementInventory | null>;
  fetchReorderSuggestions: () => Promise<void>;
  fetchStockLevelReport: () => Promise<void>;
  setSelectedInventoryItem: (item: ProcurementInventory | null) => void;
  setInventoryFilters: (filters: InventoryFilters) => void;
  clearInventoryError: () => void;

  // Cache actions
  clearCache: () => void;
  isDataStale: (timestamp: number) => boolean;
}

// API base URL
const API_BASE = '/api/procurement';

// Create the procurement store
export const useProcurementStore = create<ProcurementState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        contracts: [],
        selectedContract: null,
        isLoadingContracts: false,
        contractsError: null,
        contractFilters: {},
        contractsPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        deliveries: [],
        selectedDelivery: null,
        isLoadingDeliveries: false,
        deliveriesError: null,
        deliveryFilters: {},
        deliveriesPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        categories: [],
        categoryHierarchy: [],
        selectedCategory: null,
        isLoadingCategories: false,
        categoriesError: null,
        categoryFilters: {},
        categoriesPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        suppliers: [],
        selectedSupplier: null,
        isLoadingSuppliers: false,
        suppliersError: null,
        supplierFilters: {},
        suppliersPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        budgetCategories: [],
        isLoadingBudgetCategories: false,
        budgetCategoriesError: null,

        costCenters: [],
        isLoadingCostCenters: false,
        costCentersError: null,

        purchaseOrders: [],
        selectedPurchaseOrder: null,
        isLoadingPurchaseOrders: false,
        purchaseOrdersError: null,
        purchaseOrderFilters: {},
        purchaseOrdersPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        requisitions: [],
        selectedRequisition: null,
        isLoadingRequisitions: false,
        requisitionsError: null,
        requisitionFilters: {},
        requisitionsPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        tenders: [],
        selectedTender: null,
        isLoadingTenders: false,
        tendersError: null,
        tenderFilters: {},
        tendersPagination: { page: 1, limit: 20, total: 0, pages: 0 },

        inventoryItems: [],
        inventory: [],
        selectedInventoryItem: null,
        isLoadingInventory: false,
        inventoryError: null,
        inventoryFilters: {},
        inventoryPagination: { page: 1, limit: 20, total: 0, pages: 0 },
        reorderSuggestions: [],
        stockLevelReport: null,
        isLoadingReorderSuggestions: false,
        isLoadingStockReport: false,

        // Cache
        contractsCache: {},
        deliveriesCache: {},
        categoriesCache: {},
        suppliersCache: {},
        purchaseOrdersCache: {},
        requisitionsCache: {},
        tendersCache: {},
        inventoryCache: {},
        hierarchyCache: null,
        cacheDuration: 5 * 60 * 1000, // 5 minutes

        // Cache helper
        isDataStale: (timestamp: number) => {
          return Date.now() - timestamp > get().cacheDuration;
        },

        // Contract actions
        fetchContracts: async (page = 1, limit = 20, filters = {}) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            // Check cache
            const cacheKey = JSON.stringify({ page, limit, filters });
            const cached = get().contractsCache[cacheKey];
            if (cached && !get().isDataStale(cached.timestamp)) {
              set({
                contracts: cached.data,
                isLoadingContracts: false,
                contractsPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
              });
              return;
            }

            // Build query parameters
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('limit', limit.toString());
            
            Object.entries(filters).forEach(([key, value]) => {
              if (value !== undefined && value !== '') {
                params.append(key, value.toString());
              }
            });

            const response = await fetch(`${API_BASE}/contracts?${params}`);
            if (!response.ok) {
              throw new Error('Failed to fetch contracts');
            }

            const result = await response.json();
            
            if (result.success) {
              // Update cache
              const newCache = { ...get().contractsCache };
              newCache[cacheKey] = { data: result.data, timestamp: Date.now() };

              set({
                contracts: result.data,
                contractsPagination: result.pagination,
                contractsCache: newCache,
                isLoadingContracts: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch contracts');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        fetchContract: async (id: string) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            const response = await fetch(`${API_BASE}/contracts/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch contract');
            }

            const result = await response.json();
            
            if (result.success) {
              set({
                selectedContract: result.data,
                isLoadingContracts: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch contract');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        createContract: async (data: any) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            const response = await fetch(`${API_BASE}/contracts`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to create contract');
            }

            const result = await response.json();
            
            if (result.success) {
              // Clear cache to force refresh
              set({ contractsCache: {}, isLoadingContracts: false });
              
              toast({
                title: 'Success',
                description: 'Contract created successfully',
              });
              
              return result.data;
            } else {
              throw new Error(result.error || 'Failed to create contract');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        updateContract: async (id: string, data: any) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            const response = await fetch(`${API_BASE}/contracts/${id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to update contract');
            }

            const result = await response.json();
            
            if (result.success) {
              // Clear cache to force refresh
              set({ contractsCache: {}, isLoadingContracts: false });
              
              toast({
                title: 'Success',
                description: 'Contract updated successfully',
              });
              
              return result.data;
            } else {
              throw new Error(result.error || 'Failed to update contract');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        deleteContract: async (id: string) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            const response = await fetch(`${API_BASE}/contracts/${id}`, {
              method: 'DELETE',
            });

            if (!response.ok) {
              throw new Error('Failed to delete contract');
            }

            const result = await response.json();
            
            if (result.success) {
              // Clear cache to force refresh
              set({ contractsCache: {}, isLoadingContracts: false });
              
              toast({
                title: 'Success',
                description: 'Contract deleted successfully',
              });
              
              return true;
            } else {
              throw new Error(result.error || 'Failed to delete contract');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return false;
          }
        },

        renewContract: async (id: string, data: any) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            const response = await fetch(`${API_BASE}/contracts/${id}/renew`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to renew contract');
            }

            const result = await response.json();
            
            if (result.success) {
              // Clear cache to force refresh
              set({ contractsCache: {}, isLoadingContracts: false });
              
              toast({
                title: 'Success',
                description: 'Contract renewed successfully',
              });
              
              return result.data;
            } else {
              throw new Error(result.error || 'Failed to renew contract');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        terminateContract: async (id: string, data: any) => {
          try {
            set({ isLoadingContracts: true, contractsError: null });

            const response = await fetch(`${API_BASE}/contracts/${id}/terminate`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to terminate contract');
            }

            const result = await response.json();
            
            if (result.success) {
              // Clear cache to force refresh
              set({ contractsCache: {}, isLoadingContracts: false });
              
              toast({
                title: 'Success',
                description: 'Contract terminated successfully',
              });
              
              return result.data;
            } else {
              throw new Error(result.error || 'Failed to terminate contract');
            }
          } catch (error: any) {
            set({
              contractsError: error.message,
              isLoadingContracts: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        setSelectedContract: (contract: Contract | null) => {
          set({ selectedContract: contract });
        },

        setContractFilters: (filters: ContractFilters) => {
          set({ contractFilters: { ...get().contractFilters, ...filters } });
        },

        clearContractsError: () => {
          set({ contractsError: null });
        },

        // Delivery actions
        fetchDeliveries: async (page = 1, limit = 20, filters = {}) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            // Check cache
            const cacheKey = JSON.stringify({ page, limit, filters });
            const cached = get().deliveriesCache[cacheKey];
            if (cached && !get().isDataStale(cached.timestamp)) {
              set({
                deliveries: cached.data,
                isLoadingDeliveries: false,
                deliveriesPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
              });
              return;
            }

            // Build query parameters
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('limit', limit.toString());

            Object.entries(filters).forEach(([key, value]) => {
              if (value !== undefined && value !== '') {
                params.append(key, value.toString());
              }
            });

            const response = await fetch(`${API_BASE}/deliveries?${params}`);
            if (!response.ok) {
              throw new Error('Failed to fetch deliveries');
            }

            const result = await response.json();

            if (result.success) {
              // Update cache
              const newCache = { ...get().deliveriesCache };
              newCache[cacheKey] = { data: result.data, timestamp: Date.now() };

              set({
                deliveries: result.data,
                deliveriesPagination: result.pagination,
                deliveriesCache: newCache,
                isLoadingDeliveries: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch deliveries');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        fetchDelivery: async (id: string) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch delivery');
            }

            const result = await response.json();

            if (result.success) {
              set({
                selectedDelivery: result.data,
                isLoadingDeliveries: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch delivery');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        createDelivery: async (data: any) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to create delivery');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ deliveriesCache: {}, isLoadingDeliveries: false });

              toast({
                title: 'Success',
                description: 'Delivery created successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to create delivery');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        updateDelivery: async (id: string, data: any) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries/${id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to update delivery');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ deliveriesCache: {}, isLoadingDeliveries: false });

              toast({
                title: 'Success',
                description: 'Delivery updated successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to update delivery');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        deleteDelivery: async (id: string) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries/${id}`, {
              method: 'DELETE',
            });

            if (!response.ok) {
              throw new Error('Failed to delete delivery');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ deliveriesCache: {}, isLoadingDeliveries: false });

              toast({
                title: 'Success',
                description: 'Delivery deleted successfully',
              });

              return true;
            } else {
              throw new Error(result.error || 'Failed to delete delivery');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return false;
          }
        },

        updateDeliveryStatus: async (id: string, status: string, data = {}) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries/${id}/status`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ status, ...data }),
            });

            if (!response.ok) {
              throw new Error('Failed to update delivery status');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ deliveriesCache: {}, isLoadingDeliveries: false });

              toast({
                title: 'Success',
                description: 'Delivery status updated successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to update delivery status');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        recordGoodsReceipt: async (id: string, data: any) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries/${id}/receipt`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to record goods receipt');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ deliveriesCache: {}, isLoadingDeliveries: false });

              toast({
                title: 'Success',
                description: 'Goods receipt recorded successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to record goods receipt');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        conductInspection: async (id: string, data: any) => {
          try {
            set({ isLoadingDeliveries: true, deliveriesError: null });

            const response = await fetch(`${API_BASE}/deliveries/${id}/inspection`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to conduct inspection');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ deliveriesCache: {}, isLoadingDeliveries: false });

              toast({
                title: 'Success',
                description: 'Inspection conducted successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to conduct inspection');
            }
          } catch (error: any) {
            set({
              deliveriesError: error.message,
              isLoadingDeliveries: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        setSelectedDelivery: (delivery: Delivery | null) => {
          set({ selectedDelivery: delivery });
        },

        setDeliveryFilters: (filters: DeliveryFilters) => {
          set({ deliveryFilters: { ...get().deliveryFilters, ...filters } });
        },

        clearDeliveriesError: () => {
          set({ deliveriesError: null });
        },

        // Category actions
        fetchCategories: async (page = 1, limit = 20, filters = {}) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            // Check cache
            const cacheKey = JSON.stringify({ page, limit, filters });
            const cached = get().categoriesCache[cacheKey];
            if (cached && !get().isDataStale(cached.timestamp)) {
              set({
                categories: cached.data,
                isLoadingCategories: false,
                categoriesPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
              });
              return;
            }

            // Build query parameters
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('limit', limit.toString());

            Object.entries(filters).forEach(([key, value]) => {
              if (value !== undefined && value !== '') {
                params.append(key, value.toString());
              }
            });

            const response = await fetch(`${API_BASE}/categories?${params}`);
            if (!response.ok) {
              throw new Error('Failed to fetch categories');
            }

            const result = await response.json();

            if (result.success) {
              // Update cache
              const newCache = { ...get().categoriesCache };
              newCache[cacheKey] = { data: result.data, timestamp: Date.now() };

              set({
                categories: result.data,
                categoriesPagination: result.pagination,
                categoriesCache: newCache,
                isLoadingCategories: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch categories');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        fetchCategoryHierarchy: async (rootId?: string) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            // Check cache
            const cached = get().hierarchyCache;
            if (cached && !get().isDataStale(cached.timestamp)) {
              set({
                categoryHierarchy: cached.data,
                isLoadingCategories: false
              });
              return;
            }

            const params = new URLSearchParams();
            if (rootId) {
              params.append('rootCategoryId', rootId);
            }

            const response = await fetch(`${API_BASE}/categories/hierarchy?${params}`);
            if (!response.ok) {
              throw new Error('Failed to fetch category hierarchy');
            }

            const result = await response.json();

            if (result.success) {
              // Update cache
              set({
                categoryHierarchy: result.data,
                hierarchyCache: { data: result.data, timestamp: Date.now() },
                isLoadingCategories: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch category hierarchy');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        fetchCategory: async (id: string) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            const response = await fetch(`${API_BASE}/categories/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch category');
            }

            const result = await response.json();

            if (result.success) {
              set({
                selectedCategory: result.data,
                isLoadingCategories: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch category');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        createCategory: async (data: any) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            // Transform the data to match API expectations
            const transformedData = {
              ...data,
              // Transform parentCategoryId to parentCategory
              parentCategory: data.parentCategoryId && data.parentCategoryId !== 'none' ? data.parentCategoryId : undefined,
              // Transform budgetCategoryId to budgetCategory
              budgetCategory: data.budgetCategoryId && data.budgetCategoryId !== 'none' ? data.budgetCategoryId : undefined,
              // Remove the original fields
              parentCategoryId: undefined,
              budgetCategoryId: undefined,
              level: undefined, // Let the middleware handle this
            };

            // Remove undefined fields
            Object.keys(transformedData).forEach(key => {
              if (transformedData[key] === undefined) {
                delete transformedData[key];
              }
            });

            const response = await fetch(`${API_BASE}/categories`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(transformedData),
            });

            if (!response.ok) {
              throw new Error('Failed to create category');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ categoriesCache: {}, hierarchyCache: null, isLoadingCategories: false });

              toast({
                title: 'Success',
                description: 'Category created successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to create category');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        updateCategory: async (id: string, data: any) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            // Transform the data to match API expectations
            const transformedData = {
              ...data,
              // Transform parentCategoryId to parentCategory
              parentCategory: data.parentCategoryId && data.parentCategoryId !== 'none' ? data.parentCategoryId : undefined,
              // Transform budgetCategoryId to budgetCategory
              budgetCategory: data.budgetCategoryId && data.budgetCategoryId !== 'none' ? data.budgetCategoryId : undefined,
              // Remove the original fields
              parentCategoryId: undefined,
              budgetCategoryId: undefined,
              level: undefined, // Let the middleware handle this
            };

            // Remove undefined fields
            Object.keys(transformedData).forEach(key => {
              if (transformedData[key] === undefined) {
                delete transformedData[key];
              }
            });

            const response = await fetch(`${API_BASE}/categories/${id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(transformedData),
            });

            if (!response.ok) {
              throw new Error('Failed to update category');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ categoriesCache: {}, hierarchyCache: null, isLoadingCategories: false });

              toast({
                title: 'Success',
                description: 'Category updated successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to update category');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        deleteCategory: async (id: string) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            const response = await fetch(`${API_BASE}/categories/${id}`, {
              method: 'DELETE',
            });

            if (!response.ok) {
              throw new Error('Failed to delete category');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ categoriesCache: {}, hierarchyCache: null, isLoadingCategories: false });

              toast({
                title: 'Success',
                description: 'Category deleted successfully',
              });

              return true;
            } else {
              throw new Error(result.error || 'Failed to delete category');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return false;
          }
        },

        validateApprovalLimits: async (categoryId: string, amount: number, userId?: string) => {
          try {
            set({ isLoadingCategories: true, categoriesError: null });

            const response = await fetch(`${API_BASE}/categories/${categoryId}/validate-approval`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ amount, userId }),
            });

            if (!response.ok) {
              throw new Error('Failed to validate approval limits');
            }

            const result = await response.json();

            if (result.success) {
              set({ isLoadingCategories: false });
              return result.data;
            } else {
              throw new Error(result.error || 'Failed to validate approval limits');
            }
          } catch (error: any) {
            set({
              categoriesError: error.message,
              isLoadingCategories: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        setSelectedCategory: (category: ProcurementCategory | null) => {
          set({ selectedCategory: category });
        },

        setCategoryFilters: (filters: CategoryFilters) => {
          set({ categoryFilters: { ...get().categoryFilters, ...filters } });
        },

        clearCategoriesError: () => {
          set({ categoriesError: null });
        },

        // Supplier actions
        fetchSuppliers: async (page = 1, limit = 20, filters = {}) => {
          const cacheKey = `${page}-${limit}-${JSON.stringify(filters)}`;
          const cached = get().suppliersCache[cacheKey];

          if (cached && !get().isDataStale(cached.timestamp)) {
            set({
              suppliers: cached.data,
              suppliersPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
            });
            return;
          }

          set({ isLoadingSuppliers: true, suppliersError: null });

          try {
            const queryParams = new URLSearchParams({
              page: page.toString(),
              limit: limit.toString(),
              ...filters
            });

            console.log('Fetching suppliers from:', `/api/procurement/suppliers?${queryParams}`);
            const response = await fetch(`/api/procurement/suppliers?${queryParams}`);
            console.log('Suppliers response status:', response.status);

            if (!response.ok) {
              const errorText = await response.text();
              console.error('Suppliers fetch error:', errorText);
              throw new Error(`Failed to fetch suppliers: ${response.status} ${errorText}`);
            }

            const data = await response.json();
            console.log('Suppliers data received:', data);

            set({
              suppliers: data.data || [],
              suppliersPagination: {
                page: data.pagination?.page || page,
                limit: data.pagination?.limit || limit,
                total: data.pagination?.total || 0,
                pages: data.pagination?.pages || 0
              },
              isLoadingSuppliers: false
            });

            // Cache the results
            set({
              suppliersCache: {
                ...get().suppliersCache,
                [cacheKey]: { data: data.data || [], timestamp: Date.now() }
              }
            });
          } catch (error) {
            console.error('Error fetching suppliers:', error);
            set({
              suppliersError: error instanceof Error ? error.message : 'Failed to fetch suppliers',
              isLoadingSuppliers: false
            });
          }
        },

        fetchSupplier: async (id: string) => {
          set({ isLoadingSuppliers: true, suppliersError: null });

          try {
            const response = await fetch(`/api/procurement/suppliers/${id}`);
            if (!response.ok) throw new Error('Failed to fetch supplier');

            const supplier = await response.json();
            set({ selectedSupplier: supplier, isLoadingSuppliers: false });
          } catch (error) {
            set({
              suppliersError: error instanceof Error ? error.message : 'Failed to fetch supplier',
              isLoadingSuppliers: false
            });
          }
        },

        createSupplier: async (data: any) => {
          set({ isLoadingSuppliers: true, suppliersError: null });

          try {
            const response = await fetch('/api/procurement/suppliers', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to create supplier');

            const supplier = await response.json();

            set({
              suppliers: [supplier, ...get().suppliers],
              selectedSupplier: supplier,
              isLoadingSuppliers: false,
              suppliersCache: {} // Clear cache
            });

            return supplier;
          } catch (error) {
            set({
              suppliersError: error instanceof Error ? error.message : 'Failed to create supplier',
              isLoadingSuppliers: false
            });
            return null;
          }
        },

        updateSupplier: async (id: string, data: any) => {
          set({ isLoadingSuppliers: true, suppliersError: null });

          try {
            const response = await fetch(`/api/procurement/suppliers/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to update supplier');

            const supplier = await response.json();

            set({
              suppliers: get().suppliers.map(s => s._id === id ? supplier : s),
              selectedSupplier: supplier,
              isLoadingSuppliers: false,
              suppliersCache: {} // Clear cache
            });

            return supplier;
          } catch (error) {
            set({
              suppliersError: error instanceof Error ? error.message : 'Failed to update supplier',
              isLoadingSuppliers: false
            });
            return null;
          }
        },

        deleteSupplier: async (id: string) => {
          set({ isLoadingSuppliers: true, suppliersError: null });

          try {
            const response = await fetch(`/api/procurement/suppliers/${id}`, {
              method: 'DELETE'
            });

            if (!response.ok) throw new Error('Failed to delete supplier');

            set({
              suppliers: get().suppliers.filter(s => s._id !== id),
              selectedSupplier: get().selectedSupplier?._id === id ? null : get().selectedSupplier,
              isLoadingSuppliers: false,
              suppliersCache: {} // Clear cache
            });

            return true;
          } catch (error) {
            set({
              suppliersError: error instanceof Error ? error.message : 'Failed to delete supplier',
              isLoadingSuppliers: false
            });
            return false;
          }
        },

        setSelectedSupplier: (supplier: Supplier | null) => {
          set({ selectedSupplier: supplier });
        },

        setSupplierFilters: (filters: any) => {
          set({ supplierFilters: { ...get().supplierFilters, ...filters } });
        },

        clearSuppliersError: () => {
          set({ suppliersError: null });
        },

        // Purchase Order actions
        fetchPurchaseOrders: async (page = 1, limit = 20, filters = {}) => {
          const cacheKey = `${page}-${limit}-${JSON.stringify(filters)}`;
          const cached = get().purchaseOrdersCache[cacheKey];

          if (cached && !get().isDataStale(cached.timestamp)) {
            set({
              purchaseOrders: cached.data,
              purchaseOrdersPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
            });
            return;
          }

          set({ isLoadingPurchaseOrders: true, purchaseOrdersError: null });

          try {
            const queryParams = new URLSearchParams({
              page: page.toString(),
              limit: limit.toString(),
              ...filters
            });

            const response = await fetch(`/api/procurement/purchase-orders?${queryParams}`);
            if (!response.ok) throw new Error('Failed to fetch purchase orders');

            const data = await response.json();

            set({
              purchaseOrders: data.purchaseOrders || [],
              purchaseOrdersPagination: {
                page: data.page || page,
                limit: data.limit || limit,
                total: data.total || 0,
                pages: data.pages || 0
              },
              isLoadingPurchaseOrders: false
            });

            // Cache the results
            set({
              purchaseOrdersCache: {
                ...get().purchaseOrdersCache,
                [cacheKey]: { data: data.purchaseOrders || [], timestamp: Date.now() }
              }
            });
          } catch (error) {
            set({
              purchaseOrdersError: error instanceof Error ? error.message : 'Failed to fetch purchase orders',
              isLoadingPurchaseOrders: false
            });
          }
        },

        fetchPurchaseOrder: async (id: string) => {
          set({ isLoadingPurchaseOrders: true, purchaseOrdersError: null });

          try {
            const response = await fetch(`/api/procurement/purchase-orders/${id}`);
            if (!response.ok) throw new Error('Failed to fetch purchase order');

            const purchaseOrder = await response.json();
            set({ selectedPurchaseOrder: purchaseOrder, isLoadingPurchaseOrders: false });
          } catch (error) {
            set({
              purchaseOrdersError: error instanceof Error ? error.message : 'Failed to fetch purchase order',
              isLoadingPurchaseOrders: false
            });
          }
        },

        createPurchaseOrder: async (data: any) => {
          set({ isLoadingPurchaseOrders: true, purchaseOrdersError: null });

          try {
            const response = await fetch('/api/procurement/purchase-orders', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to create purchase order');

            const purchaseOrder = await response.json();

            set({
              purchaseOrders: [purchaseOrder, ...get().purchaseOrders],
              selectedPurchaseOrder: purchaseOrder,
              isLoadingPurchaseOrders: false,
              purchaseOrdersCache: {} // Clear cache
            });

            return purchaseOrder;
          } catch (error) {
            set({
              purchaseOrdersError: error instanceof Error ? error.message : 'Failed to create purchase order',
              isLoadingPurchaseOrders: false
            });
            return null;
          }
        },

        updatePurchaseOrder: async (id: string, data: any) => {
          set({ isLoadingPurchaseOrders: true, purchaseOrdersError: null });

          try {
            const response = await fetch(`/api/procurement/purchase-orders/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to update purchase order');

            const purchaseOrder = await response.json();

            set({
              purchaseOrders: get().purchaseOrders.map(po => po._id === id ? purchaseOrder : po),
              selectedPurchaseOrder: purchaseOrder,
              isLoadingPurchaseOrders: false,
              purchaseOrdersCache: {} // Clear cache
            });

            return purchaseOrder;
          } catch (error) {
            set({
              purchaseOrdersError: error instanceof Error ? error.message : 'Failed to update purchase order',
              isLoadingPurchaseOrders: false
            });
            return null;
          }
        },

        deletePurchaseOrder: async (id: string) => {
          set({ isLoadingPurchaseOrders: true, purchaseOrdersError: null });

          try {
            const response = await fetch(`/api/procurement/purchase-orders/${id}`, {
              method: 'DELETE'
            });

            if (!response.ok) throw new Error('Failed to delete purchase order');

            set({
              purchaseOrders: get().purchaseOrders.filter(po => po._id !== id),
              selectedPurchaseOrder: get().selectedPurchaseOrder?._id === id ? null : get().selectedPurchaseOrder,
              isLoadingPurchaseOrders: false,
              purchaseOrdersCache: {} // Clear cache
            });

            return true;
          } catch (error) {
            set({
              purchaseOrdersError: error instanceof Error ? error.message : 'Failed to delete purchase order',
              isLoadingPurchaseOrders: false
            });
            return false;
          }
        },

        approvePurchaseOrder: async (id: string, data: any) => {
          set({ isLoadingPurchaseOrders: true, purchaseOrdersError: null });

          try {
            const response = await fetch(`/api/procurement/purchase-orders/${id}/approve`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to approve purchase order');

            const purchaseOrder = await response.json();

            set({
              purchaseOrders: get().purchaseOrders.map(po => po._id === id ? purchaseOrder : po),
              selectedPurchaseOrder: purchaseOrder,
              isLoadingPurchaseOrders: false,
              purchaseOrdersCache: {} // Clear cache
            });

            return purchaseOrder;
          } catch (error) {
            set({
              purchaseOrdersError: error instanceof Error ? error.message : 'Failed to approve purchase order',
              isLoadingPurchaseOrders: false
            });
            return null;
          }
        },

        setSelectedPurchaseOrder: (purchaseOrder: PurchaseOrder | null) => {
          set({ selectedPurchaseOrder: purchaseOrder });
        },

        setPurchaseOrderFilters: (filters: any) => {
          set({ purchaseOrderFilters: { ...get().purchaseOrderFilters, ...filters } });
        },

        clearPurchaseOrdersError: () => {
          set({ purchaseOrdersError: null });
        },

        // Requisition actions
        fetchRequisitions: async (page = 1, limit = 20, filters = {}) => {
          const cacheKey = `${page}-${limit}-${JSON.stringify(filters)}`;
          const cached = get().requisitionsCache[cacheKey];

          if (cached && !get().isDataStale(cached.timestamp)) {
            set({
              requisitions: cached.data,
              requisitionsPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
            });
            return;
          }

          set({ isLoadingRequisitions: true, requisitionsError: null });

          try {
            const queryParams = new URLSearchParams({
              page: page.toString(),
              limit: limit.toString(),
              ...filters
            });

            const response = await fetch(`/api/procurement/requisitions?${queryParams}`);
            if (!response.ok) throw new Error('Failed to fetch requisitions');

            const data = await response.json();

            set({
              requisitions: data.requisitions || [],
              requisitionsPagination: {
                page: data.page || page,
                limit: data.limit || limit,
                total: data.total || 0,
                pages: data.pages || 0
              },
              isLoadingRequisitions: false
            });

            // Cache the results
            set({
              requisitionsCache: {
                ...get().requisitionsCache,
                [cacheKey]: { data: data.requisitions || [], timestamp: Date.now() }
              }
            });
          } catch (error) {
            set({
              requisitionsError: error instanceof Error ? error.message : 'Failed to fetch requisitions',
              isLoadingRequisitions: false
            });
          }
        },

        fetchRequisition: async (id: string) => {
          set({ isLoadingRequisitions: true, requisitionsError: null });

          try {
            const response = await fetch(`/api/procurement/requisitions/${id}`);
            if (!response.ok) throw new Error('Failed to fetch requisition');

            const requisition = await response.json();
            set({ selectedRequisition: requisition, isLoadingRequisitions: false });
          } catch (error) {
            set({
              requisitionsError: error instanceof Error ? error.message : 'Failed to fetch requisition',
              isLoadingRequisitions: false
            });
          }
        },

        createRequisition: async (data: any) => {
          set({ isLoadingRequisitions: true, requisitionsError: null });

          try {
            const response = await fetch('/api/procurement/requisition', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.message || 'Failed to create requisition');
            }

            const requisition = await response.json();

            set({
              requisitions: [requisition, ...get().requisitions],
              selectedRequisition: requisition,
              isLoadingRequisitions: false,
              requisitionsCache: {} // Clear cache
            });

            toast({
              title: "Success",
              description: "Purchase requisition created successfully!",
            });

            return requisition;
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create requisition';
            set({
              requisitionsError: errorMessage,
              isLoadingRequisitions: false
            });

            toast({
              title: "Error",
              description: errorMessage,
              variant: "destructive",
            });

            throw error; // Re-throw to allow caller to handle
          }
        },

        updateRequisition: async (id: string, data: any) => {
          set({ isLoadingRequisitions: true, requisitionsError: null });

          try {
            const response = await fetch(`/api/procurement/requisitions/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to update requisition');

            const requisition = await response.json();

            set({
              requisitions: get().requisitions.map(r => r._id === id ? requisition : r),
              selectedRequisition: requisition,
              isLoadingRequisitions: false,
              requisitionsCache: {} // Clear cache
            });

            return requisition;
          } catch (error) {
            set({
              requisitionsError: error instanceof Error ? error.message : 'Failed to update requisition',
              isLoadingRequisitions: false
            });
            return null;
          }
        },

        deleteRequisition: async (id: string) => {
          set({ isLoadingRequisitions: true, requisitionsError: null });

          try {
            const response = await fetch(`/api/procurement/requisitions/${id}`, {
              method: 'DELETE'
            });

            if (!response.ok) throw new Error('Failed to delete requisition');

            set({
              requisitions: get().requisitions.filter(r => r._id !== id),
              selectedRequisition: get().selectedRequisition?._id === id ? null : get().selectedRequisition,
              isLoadingRequisitions: false,
              requisitionsCache: {} // Clear cache
            });

            return true;
          } catch (error) {
            set({
              requisitionsError: error instanceof Error ? error.message : 'Failed to delete requisition',
              isLoadingRequisitions: false
            });
            return false;
          }
        },

        approveRequisition: async (id: string, data: any) => {
          set({ isLoadingRequisitions: true, requisitionsError: null });

          try {
            const response = await fetch(`/api/procurement/requisitions/${id}/approve`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to approve requisition');

            const requisition = await response.json();

            set({
              requisitions: get().requisitions.map(r => r._id === id ? requisition : r),
              selectedRequisition: requisition,
              isLoadingRequisitions: false,
              requisitionsCache: {} // Clear cache
            });

            return requisition;
          } catch (error) {
            set({
              requisitionsError: error instanceof Error ? error.message : 'Failed to approve requisition',
              isLoadingRequisitions: false
            });
            return null;
          }
        },

        setSelectedRequisition: (requisition: Requisition | null) => {
          set({ selectedRequisition: requisition });
        },

        setRequisitionFilters: (filters: any) => {
          set({ requisitionFilters: { ...get().requisitionFilters, ...filters } });
        },

        clearRequisitionsError: () => {
          set({ requisitionsError: null });
        },

        // Tender actions
        fetchTenders: async (page = 1, limit = 20, filters = {}) => {
          const cacheKey = `${page}-${limit}-${JSON.stringify(filters)}`;
          const cached = get().tendersCache[cacheKey];

          if (cached && !get().isDataStale(cached.timestamp)) {
            set({
              tenders: cached.data,
              tendersPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
            });
            return;
          }

          set({ isLoadingTenders: true, tendersError: null });

          try {
            const queryParams = new URLSearchParams({
              page: page.toString(),
              limit: limit.toString(),
              ...filters
            });

            const response = await fetch(`/api/procurement/tenders?${queryParams}`);
            if (!response.ok) throw new Error('Failed to fetch tenders');

            const data = await response.json();

            set({
              tenders: data.tenders || [],
              tendersPagination: {
                page: data.page || page,
                limit: data.limit || limit,
                total: data.total || 0,
                pages: data.pages || 0
              },
              isLoadingTenders: false
            });

            // Cache the results
            set({
              tendersCache: {
                ...get().tendersCache,
                [cacheKey]: { data: data.tenders || [], timestamp: Date.now() }
              }
            });
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to fetch tenders',
              isLoadingTenders: false
            });
          }
        },

        fetchTender: async (id: string) => {
          set({ isLoadingTenders: true, tendersError: null });

          try {
            const response = await fetch(`/api/procurement/tenders/${id}`);
            if (!response.ok) throw new Error('Failed to fetch tender');

            const tender = await response.json();
            set({ selectedTender: tender, isLoadingTenders: false });
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to fetch tender',
              isLoadingTenders: false
            });
          }
        },

        createTender: async (data: any) => {
          set({ isLoadingTenders: true, tendersError: null });

          try {
            const response = await fetch('/api/procurement/tenders', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to create tender');

            const tender = await response.json();

            set({
              tenders: [tender, ...get().tenders],
              selectedTender: tender,
              isLoadingTenders: false,
              tendersCache: {} // Clear cache
            });

            return tender;
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to create tender',
              isLoadingTenders: false
            });
            return null;
          }
        },

        updateTender: async (id: string, data: any) => {
          set({ isLoadingTenders: true, tendersError: null });

          try {
            const response = await fetch(`/api/procurement/tenders/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(data)
            });

            if (!response.ok) throw new Error('Failed to update tender');

            const tender = await response.json();

            set({
              tenders: get().tenders.map(t => t._id === id ? tender : t),
              selectedTender: tender,
              isLoadingTenders: false,
              tendersCache: {} // Clear cache
            });

            return tender;
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to update tender',
              isLoadingTenders: false
            });
            return null;
          }
        },

        deleteTender: async (id: string) => {
          set({ isLoadingTenders: true, tendersError: null });

          try {
            const response = await fetch(`/api/procurement/tenders/${id}`, {
              method: 'DELETE'
            });

            if (!response.ok) throw new Error('Failed to delete tender');

            set({
              tenders: get().tenders.filter(t => t._id !== id),
              selectedTender: get().selectedTender?._id === id ? null : get().selectedTender,
              isLoadingTenders: false,
              tendersCache: {} // Clear cache
            });

            return true;
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to delete tender',
              isLoadingTenders: false
            });
            return false;
          }
        },

        publishTender: async (id: string) => {
          set({ isLoadingTenders: true, tendersError: null });

          try {
            const response = await fetch(`/api/procurement/tenders/${id}/publish`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) throw new Error('Failed to publish tender');

            const tender = await response.json();

            set({
              tenders: get().tenders.map(t => t._id === id ? tender : t),
              selectedTender: tender,
              isLoadingTenders: false,
              tendersCache: {} // Clear cache
            });

            return tender;
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to publish tender',
              isLoadingTenders: false
            });
            return null;
          }
        },

        closeTender: async (id: string) => {
          set({ isLoadingTenders: true, tendersError: null });

          try {
            const response = await fetch(`/api/procurement/tenders/${id}/close`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) throw new Error('Failed to close tender');

            const tender = await response.json();

            set({
              tenders: get().tenders.map(t => t._id === id ? tender : t),
              selectedTender: tender,
              isLoadingTenders: false,
              tendersCache: {} // Clear cache
            });

            return tender;
          } catch (error) {
            set({
              tendersError: error instanceof Error ? error.message : 'Failed to close tender',
              isLoadingTenders: false
            });
            return null;
          }
        },

        setSelectedTender: (tender: Tender | null) => {
          set({ selectedTender: tender });
        },

        setTenderFilters: (filters: any) => {
          set({ tenderFilters: { ...get().tenderFilters, ...filters } });
        },

        clearTendersError: () => {
          set({ tendersError: null });
        },

        // Inventory actions
        fetchInventory: async (page = 1, limit = 20, filters = {}) => {
          try {
            set({ isLoadingInventory: true, inventoryError: null });

            // Check cache
            const cacheKey = JSON.stringify({ page, limit, filters });
            const cached = get().inventoryCache[cacheKey];
            if (cached && !get().isDataStale(cached.timestamp)) {
              set({
                inventory: cached.data,
                inventoryItems: cached.data, // For backward compatibility
                isLoadingInventory: false,
                inventoryPagination: { page, limit, total: cached.data.length, pages: Math.ceil(cached.data.length / limit) }
              });
              return;
            }

            // Build query parameters
            const params = new URLSearchParams();
            params.append('page', page.toString());
            params.append('limit', limit.toString());

            Object.entries(filters).forEach(([key, value]) => {
              if (value !== undefined && value !== '') {
                if (Array.isArray(value)) {
                  params.append(key, value.join(','));
                } else {
                  params.append(key, value.toString());
                }
              }
            });

            const response = await fetch(`${API_BASE}/inventory?${params}`);
            if (!response.ok) {
              throw new Error('Failed to fetch inventory');
            }

            const result = await response.json();

            if (result.success) {
              // Update cache
              const newCache = { ...get().inventoryCache };
              newCache[cacheKey] = { data: result.data.items, timestamp: Date.now() };

              set({
                inventory: result.data.items,
                inventoryItems: result.data.items, // For backward compatibility
                inventoryPagination: {
                  page: result.data.page,
                  limit: result.data.limit || limit,
                  total: result.data.total,
                  pages: result.data.pages
                },
                inventoryCache: newCache,
                isLoadingInventory: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch inventory');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingInventory: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        fetchInventoryItem: async (id: string) => {
          try {
            set({ isLoadingInventory: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory/${id}`);
            if (!response.ok) {
              throw new Error('Failed to fetch inventory item');
            }

            const result = await response.json();

            if (result.success) {
              set({
                selectedInventoryItem: result.data,
                isLoadingInventory: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch inventory item');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingInventory: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        createInventoryItem: async (data: any) => {
          try {
            set({ isLoadingInventory: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to create inventory item');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ inventoryCache: {}, isLoadingInventory: false });

              toast({
                title: 'Success',
                description: 'Inventory item created successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to create inventory item');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingInventory: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        updateInventoryItem: async (id: string, data: any) => {
          try {
            set({ isLoadingInventory: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory/${id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data),
            });

            if (!response.ok) {
              throw new Error('Failed to update inventory item');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ inventoryCache: {}, isLoadingInventory: false });

              toast({
                title: 'Success',
                description: 'Inventory item updated successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to update inventory item');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingInventory: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        deleteInventoryItem: async (id: string) => {
          try {
            set({ isLoadingInventory: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory/${id}`, {
              method: 'DELETE',
            });

            if (!response.ok) {
              throw new Error('Failed to delete inventory item');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ inventoryCache: {}, isLoadingInventory: false });

              toast({
                title: 'Success',
                description: 'Inventory item deleted successfully',
              });

              return true;
            } else {
              throw new Error(result.error || 'Failed to delete inventory item');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingInventory: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return false;
          }
        },

        updateStock: async (itemId: string, quantity: number, operation: 'add' | 'remove' | 'set', reason?: string) => {
          try {
            set({ isLoadingInventory: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory/stock`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ itemId, quantity, operation, reason }),
            });

            if (!response.ok) {
              throw new Error('Failed to update stock');
            }

            const result = await response.json();

            if (result.success) {
              // Clear cache to force refresh
              set({ inventoryCache: {}, isLoadingInventory: false });

              toast({
                title: 'Success',
                description: 'Stock updated successfully',
              });

              return result.data;
            } else {
              throw new Error(result.error || 'Failed to update stock');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingInventory: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
            return null;
          }
        },

        fetchReorderSuggestions: async () => {
          try {
            set({ isLoadingReorderSuggestions: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory/reorder`);
            if (!response.ok) {
              throw new Error('Failed to fetch reorder suggestions');
            }

            const result = await response.json();

            if (result.success) {
              set({
                reorderSuggestions: result.data,
                isLoadingReorderSuggestions: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch reorder suggestions');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingReorderSuggestions: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        fetchStockLevelReport: async () => {
          try {
            set({ isLoadingStockReport: true, inventoryError: null });

            const response = await fetch(`${API_BASE}/inventory/reports?type=stock-levels`);
            if (!response.ok) {
              throw new Error('Failed to fetch stock level report');
            }

            const result = await response.json();

            if (result.success) {
              set({
                stockLevelReport: result.data,
                isLoadingStockReport: false
              });
            } else {
              throw new Error(result.error || 'Failed to fetch stock level report');
            }
          } catch (error: any) {
            set({
              inventoryError: error.message,
              isLoadingStockReport: false
            });
            toast({
              title: 'Error',
              description: error.message,
              variant: 'destructive'
            });
          }
        },

        setSelectedInventoryItem: (item: ProcurementInventory | null) => {
          set({ selectedInventoryItem: item });
        },

        setInventoryFilters: (filters: InventoryFilters) => {
          set({ inventoryFilters: { ...get().inventoryFilters, ...filters } });
        },

        clearInventoryError: () => {
          set({ inventoryError: null });
        },

        // Budget Category actions
        fetchBudgetCategories: async () => {
          set({ isLoadingBudgetCategories: true, budgetCategoriesError: null });

          try {
            console.log('Fetching budget categories...');

            // First, try to get categories from all active budgets
            let response = await fetch('/api/accounting/budget/categories?type=expense');
            console.log('Budget categories response status:', response.status);

            if (!response.ok) {
              const errorText = await response.text();
              console.error('Budget categories fetch error:', errorText);

              // If that fails, try to get active budgets first
              console.log('Trying to fetch active budgets first...');
              const budgetsResponse = await fetch('/api/accounting/budget?status=active&limit=1');

              if (budgetsResponse.ok) {
                const budgetsData = await budgetsResponse.json();
                console.log('Active budgets data:', budgetsData);

                if (budgetsData.budgets && budgetsData.budgets.length > 0) {
                  const firstBudget = budgetsData.budgets[0];
                  console.log('Using first active budget:', firstBudget._id);

                  // Try to get categories from the first active budget
                  response = await fetch(`/api/accounting/budget/categories?budgetId=${firstBudget._id}&type=expense`);

                  if (!response.ok) {
                    throw new Error(`Failed to fetch budget categories: ${response.status}`);
                  }
                } else {
                  console.log('No active budgets found, using default categories');
                  // Provide default budget categories for contracts
                  const defaultCategories = [
                    { _id: 'default-services', name: 'Professional Services' },
                    { _id: 'default-supplies', name: 'Office Supplies' },
                    { _id: 'default-equipment', name: 'Equipment & Hardware' },
                    { _id: 'default-software', name: 'Software & Licenses' },
                    { _id: 'default-maintenance', name: 'Maintenance & Support' },
                    { _id: 'default-consulting', name: 'Consulting Services' },
                    { _id: 'default-training', name: 'Training & Development' },
                    { _id: 'default-other', name: 'Other Expenses' }
                  ];

                  set({
                    budgetCategories: defaultCategories,
                    isLoadingBudgetCategories: false
                  });
                  return;
                }
              } else {
                console.log('Budget API not available, using default categories');
                // Provide default budget categories for contracts
                const defaultCategories = [
                  { _id: 'default-services', name: 'Professional Services' },
                  { _id: 'default-supplies', name: 'Office Supplies' },
                  { _id: 'default-equipment', name: 'Equipment & Hardware' },
                  { _id: 'default-software', name: 'Software & Licenses' },
                  { _id: 'default-maintenance', name: 'Maintenance & Support' },
                  { _id: 'default-consulting', name: 'Consulting Services' },
                  { _id: 'default-training', name: 'Training & Development' },
                  { _id: 'default-other', name: 'Other Expenses' }
                ];

                set({
                  budgetCategories: defaultCategories,
                  isLoadingBudgetCategories: false
                });
                return;
              }
            }

            const data = await response.json();
            console.log('Budget categories data received:', data);

            set({
              budgetCategories: data.categories || [],
              isLoadingBudgetCategories: false
            });
          } catch (error) {
            console.error('Error fetching budget categories:', error);

            // Fallback to default categories on any error
            console.log('Using default categories as fallback');
            const defaultCategories = [
              { _id: 'default-services', name: 'Professional Services' },
              { _id: 'default-supplies', name: 'Office Supplies' },
              { _id: 'default-equipment', name: 'Equipment & Hardware' },
              { _id: 'default-software', name: 'Software & Licenses' },
              { _id: 'default-maintenance', name: 'Maintenance & Support' },
              { _id: 'default-consulting', name: 'Consulting Services' },
              { _id: 'default-training', name: 'Training & Development' },
              { _id: 'default-other', name: 'Other Expenses' }
            ];

            set({
              budgetCategories: defaultCategories,
              isLoadingBudgetCategories: false,
              budgetCategoriesError: null // Clear error since we have fallback data
            });
          }
        },

        clearBudgetCategoriesError: () => {
          set({ budgetCategoriesError: null });
        },

        // Cost Center actions
        fetchCostCenters: async () => {
          set({ isLoadingCostCenters: true, costCentersError: null });

          try {
            console.log('Fetching cost centers...');
            const response = await fetch('/api/accounting/cost-centers?limit=100&isActive=true');
            console.log('Cost centers response status:', response.status);

            if (!response.ok) {
              const errorText = await response.text();
              console.error('Cost centers fetch error:', errorText);

              // Provide default cost centers as fallback
              console.log('Cost centers API not available, using default cost centers');
              const defaultCostCenters = [
                { _id: 'default-admin', name: 'Administration', code: 'ADMIN' },
                { _id: 'default-hr', name: 'Human Resources', code: 'HR' },
                { _id: 'default-it', name: 'Information Technology', code: 'IT' },
                { _id: 'default-finance', name: 'Finance & Accounting', code: 'FIN' },
                { _id: 'default-ops', name: 'Operations', code: 'OPS' },
                { _id: 'default-sales', name: 'Sales & Marketing', code: 'SALES' },
                { _id: 'default-procurement', name: 'Procurement', code: 'PROC' },
                { _id: 'default-general', name: 'General Expenses', code: 'GEN' }
              ];

              set({
                costCenters: defaultCostCenters,
                isLoadingCostCenters: false,
                costCentersError: null
              });
              return;
            }

            const data = await response.json();
            console.log('Cost centers data received:', data);

            set({
              costCenters: data.data || [],
              isLoadingCostCenters: false
            });
          } catch (error) {
            console.error('Error fetching cost centers:', error);

            // Fallback to default cost centers on any error
            console.log('Using default cost centers as fallback');
            const defaultCostCenters = [
              { _id: 'default-admin', name: 'Administration', code: 'ADMIN' },
              { _id: 'default-hr', name: 'Human Resources', code: 'HR' },
              { _id: 'default-it', name: 'Information Technology', code: 'IT' },
              { _id: 'default-finance', name: 'Finance & Accounting', code: 'FIN' },
              { _id: 'default-ops', name: 'Operations', code: 'OPS' },
              { _id: 'default-sales', name: 'Sales & Marketing', code: 'SALES' },
              { _id: 'default-procurement', name: 'Procurement', code: 'PROC' },
              { _id: 'default-general', name: 'General Expenses', code: 'GEN' }
            ];

            set({
              costCenters: defaultCostCenters,
              isLoadingCostCenters: false,
              costCentersError: null // Clear error since we have fallback data
            });
          }
        },

        clearCostCentersError: () => {
          set({ costCentersError: null });
        },

        // Cache actions
        clearCache: () => {
          set({
            contractsCache: {},
            deliveriesCache: {},
            categoriesCache: {},
            suppliersCache: {},
            purchaseOrdersCache: {},
            requisitionsCache: {},
            tendersCache: {},
            inventoryCache: {},
            hierarchyCache: null
          });
        },
      }),
      {
        name: 'procurement-storage',
        partialize: (state) => ({
          contractFilters: state.contractFilters,
          deliveryFilters: state.deliveryFilters,
          categoryFilters: state.categoryFilters,
          inventoryFilters: state.inventoryFilters,
        }),
      }
    ),
    {
      name: 'procurement-store',
    }
  )
);
